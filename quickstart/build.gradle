plugins {
    id 'java'
    id 'application'
}

group = 'com.example'
version = '1.0.0'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(24)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'com.google.guava:guava:33.0.0-jre'
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.16.1'
}

application {
    mainClass = 'com.example.quickstart.Main'
}

jar {
    manifest {
        attributes(
            'Main-Class': 'com.example.quickstart.Main'
        )
    }
}

// Task to create a fat JAR for testing
task fatJar(type: Jar) {
    archiveClassifier = 'fat'
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    with jar

    manifest {
        attributes(
            'Main-Class': 'com.example.quickstart.Main'
        )
    }
}
