name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Check version
        run: |
          tag=${GITHUB_REF#refs/tags/v}
          PROJECT_VERSION=$(grep "^version=" gradle.properties | cut -d'=' -f2)
          if [[ "$PROJECT_VERSION" == "$tag" ]]; then
            echo "Version match: tag $VERSION_TAG matches project version $PROJECT_VERSION, proceeding with release"
          else
            echo "Version mismatch: tag $VERSION_TAG does not match project version $PROJECT_VERSION"
            exit 1
          fi

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: '24'
          distribution: 'corretto'
          cache: 'gradle'

      - name: Build
        run: ./gradlew build --no-daemon --stacktrace

  compile-native-image:
    name: Compile native image on ${{ matrix.os }}
    timeout-minutes: 30
    runs-on: ${{ matrix.os }}
    needs: [ build ]
    strategy:
      matrix:
        os: [ macos-latest, windows-latest, ubuntu-latest ]
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Setup GraalVM
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '24'
          distribution: 'graalvm'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          native-image-job-reports: 'true'

      - name: Build native image
        run: |
          ./gradlew :jarinker-cli:nativeCompile

      - name: Upload binary
        uses: actions/upload-artifact@v4
        with:
          name: 'jarinker-cli-${{ matrix.os }}'
          path: |
            jarinker-cli/build/native/nativeCompile/jarinker
            jarinker-cli/build/native/nativeCompile/jarinker.exe

  release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [ compile-native-image ]
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Download binaries
        uses: actions/download-artifact@v4
        with:
          pattern: 'jarinker-cli-*'

      - name: Show downloaded files
        run: ls -l jarinker-cli-*

      - name: Extract tag
        run: |
          tag=${GITHUB_REF#refs/tags/v}
          echo "TAG=${tag}" >> $GITHUB_ENV

      - name: Prepare zipped binaries
        run: |
          
          mkdir -p zipped

          mkdir -p temp/linux
          mv jarinker-cli-ubuntu-latest/jarinker temp/linux/jarinker
          zip -j zipped/jarinker-linux-${{ env.TAG }}.zip temp/linux/jarinker

          mkdir -p temp/macos
          mv jarinker-cli-macos-latest/jarinker temp/macos/jarinker
          zip -j zipped/jarinker-macos-${{ env.TAG }}.zip temp/macos/jarinker

          mkdir -p temp/windows
          mv jarinker-cli-windows-latest/jarinker.exe temp/windows/jarinker.exe
          zip -j zipped/jarinker-windows-${{ env.TAG }}.zip temp/windows/jarinker.exe

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          generate_release_notes: true
          draft: true
          files: |
            zipped/jarinker-linux-${{ env.TAG }}.zip
            zipped/jarinker-macos-${{ env.TAG }}.zip
            zipped/jarinker-windows-${{ env.TAG }}.zip
